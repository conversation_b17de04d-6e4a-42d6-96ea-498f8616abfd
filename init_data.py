
#!/usr/bin/env python3
"""
Initialize ATE MEG's FROZEN FOODS database with complete product and customer data
Includes selling price, capital price, and proper profit calculations
"""

from app import app, db, Product, Customer, Sale, Transaction, TransactionItem, CreditTransaction, Cashier, Settings, ProductTransaction
from datetime import datetime, timezone, timedelta
import uuid

def init_sample_data():
    """Initialize the database with ATE MEG's frozen foods inventory data"""

    with app.app_context():
        # Create all tables with updated schema
        db.create_all()

        # Clear existing data for fresh start
        print("🗑️ Clearing existing data...")

        # Clear all tables in proper order (foreign key dependencies)
        db.session.query(CreditTransaction).delete()
        db.session.query(TransactionItem).delete()
        db.session.query(Transaction).delete()
        db.session.query(Sale).delete()
        db.session.query(Customer).delete()
        db.session.query(Product).delete()
        db.session.query(Cashier).delete()

        db.session.commit()
        print("✅ All existing data cleared successfully")

        # ATE MEG's FROZEN FOODS - Complete inventory with selling and capital prices
        frozen_foods_data = [
            {"name": "Chicken Pastil", "selling_price": 110.00, "capital_price": 85.00, "received": 22, "sold": 0, "stock": 22},
            {"name": "Pork Sisig", "selling_price": 110.00, "capital_price": 85.00, "received": 22, "sold": 0, "stock": 22},
            {"name": "Cheesy Hamonado", "selling_price": 90.00, "capital_price": 71.00, "received": 21, "sold": 0, "stock": 21},  # Matches Cheesy Hamburgido WS
            {"name": "Pork Dinakdakan", "selling_price": 110.00, "capital_price": 85.00, "received": 22, "sold": 0, "stock": 22},
            {"name": "Chicken Teriyaki", "selling_price": 80.00, "capital_price": 68.00, "received": 16, "sold": 0, "stock": 16},
            {"name": "Chicken Tocino", "selling_price": 80.00, "capital_price": 68.00, "received": 15, "sold": 0, "stock": 15},
            {"name": "Pork Tapa", "selling_price": 75.00, "capital_price": 62.00, "received": 22, "sold": 0, "stock": 22},
            {"name": "Pork Longadog", "selling_price": 65.00, "capital_price": 45.00, "received": 21, "sold": 0, "stock": 21},  # Matches Longadoq sulit pack WS
            {"name": "Pork Teriyaki", "selling_price": 75.00, "capital_price": 62.00, "received": 24, "sold": 0, "stock": 24},  # Honey Pork Teriyaki
            {"name": "Pork Tocino", "selling_price": 75.00, "capital_price": 58.00, "received": 20, "sold": 0, "stock": 20},
            {"name": "Skinless Longanisa", "selling_price": 65.00, "capital_price": 45.00, "received": 16, "sold": 0, "stock": 16},
            {"name": "Pork Longanisa", "selling_price": 65.00, "capital_price": 45.00, "received": 15, "sold": 0, "stock": 15},  # Smoked Longanisa
            {"name": "Big Siomai", "selling_price": 70.00, "capital_price": 48.00, "received": 22, "sold": 0, "stock": 22},     # Pork Siomai
            {"name": "Salami w/ Cheese", "selling_price": 90.00, "capital_price": 67.00, "received": 11, "sold": 0, "stock": 11},
            {"name": "Pork Meatballs", "selling_price": 60.00, "capital_price": 43.00, "received": 20, "sold": 0, "stock": 20},
            {"name": "Chicken Nuggets", "selling_price": 60.00, "capital_price": 43.00, "received": 20, "sold": 0, "stock": 20},
            {"name": "Sliced Ham", "selling_price": 75.00, "capital_price": 54.00, "received": 10, "sold": 0, "stock": 10},
            {"name": "Beef Burger Patty", "selling_price": 80.00, "capital_price": 54.00, "received": 21, "sold": 0, "stock": 21}  # Burger patty sulit WS
        ]

        print("🏪 Initializing ATE MEG's FROZEN FOODS inventory...")
        print("=" * 60)

        for item_data in frozen_foods_data:
            # Create product with selling price and capital price
            product = Product(
                name=item_data["name"],
                price=item_data["selling_price"],  # Selling price
                received=item_data["received"],
                sold=item_data["sold"],
                stock=item_data["stock"],
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )

            # Set capital price using the model's method
            product.set_capital_price(item_data["capital_price"])

            db.session.add(product)

            # Calculate profit for display
            profit_per_unit = item_data["selling_price"] - item_data["capital_price"]
            total_profit = 0  # No profit yet since nothing sold

            print(f"✅ {item_data['name']:<20} | Selling: ₱{item_data['selling_price']:<6.2f} | Capital: ₱{item_data['capital_price']:<6.2f} | Profit/Unit: ₱{profit_per_unit:<6.2f} | Stock: {item_data['stock']:<3} | Total Profit: ₱{total_profit:<7.2f}")

        db.session.commit()
        print("=" * 60)
        print(f"🎉 Successfully initialized {len(frozen_foods_data)} products!")

        # Calculate and display summary
        total_revenue = 0  # No sales yet
        total_capital_invested = sum(item["received"] * item["capital_price"] for item in frozen_foods_data)
        total_profit = 0  # No profit yet

        print(f"\n📊 BUSINESS SUMMARY:")
        print(f"   💰 Total Revenue: ₱{total_revenue:,.2f}")
        print(f"   💸 Capital Invested: ₱{total_capital_invested:,.2f}")
        print(f"   📈 Total Profit: ₱{total_profit:,.2f}")
        print(f"   📊 Profit Margin: 0.0% (No sales yet)")

        # Add sample customers for credit management
        sample_customers = [
            {"name": "Bets", "phone": "***********", "address": "sa tabi ng inomart"},
            {"name": "Chelsea", "phone": "***********", "address": "Sta Cruz, Laguna"},
            {"name": "Ate Joy", "phone": "***********", "address": "Sta Cruz, Laguna"}
        ]

        print(f"\n👥 Adding sample customers...")
        for customer_data in sample_customers:
            customer = Customer(
                name=customer_data["name"],
                phone=customer_data["phone"],
                address=customer_data["address"],
                total_credit=0.0,  # Reset to zero (original state)
                total_paid=0.0,    # Reset to zero (original state)
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            db.session.add(customer)
            print(f"   ✅ {customer_data['name']} - {customer_data['phone']} (Balance: ₱0.00)")

        db.session.commit()
        print(f"\n🎉 Successfully initialized {len(sample_customers)} sample customers!")
        print("💰 All customers start with zero credit balance (original state)")

        # Add cashiers
        cashiers_data = [
            {"name": "Abbie"},
            {"name": "Nonoy"},
            {"name": "Gian"},
            {"name": "Caleb"},
            {"name": "Yasha"},
            {"name": "House"},
            {"name": "Mama"}
        ]

        print(f"\n👥 Adding cashiers...")
        for cashier_data in cashiers_data:
            cashier = Cashier(
                name=cashier_data["name"],
                is_active=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            db.session.add(cashier)
            print(f"   ✅ {cashier_data['name']} - Active")

        db.session.commit()
        print(f"\n🎉 Successfully initialized {len(cashiers_data)} cashiers!")

        print("\n" + "=" * 60)
        print("🏪 ATE MEG's FROZEN FOODS DATABASE READY!")
        print("=" * 60)
        print("📋 Features Available:")
        print("   • Product Management with Profit Calculations")
        print("   • Sales Tracking and Reports")
        print("   • Inventory Management")
        print("   • Customer Credit Management")
        print("   • Cashier Management and Transaction Tracking")
        print("   • Transaction History with Cashier Info")
        print("   • Financial Analytics")
        print("   • Automatic CSV Backup System")

        print("\n🗄️ Database Tables Created:")
        print("   📦 Products - Inventory with selling/capital prices")
        print("   👥 Customers - Customer information and credit tracking")
        print("   🧑‍💼 Cashiers - Staff members who process transactions")
        print("   💰 Sales - Individual sale records")
        print("   📋 Transactions - Complete transaction records with cashier info")
        print("   📝 TransactionItems - Detailed transaction line items")
        print("   💳 CreditTransactions - Credit and payment tracking")

        print("\n🔧 Important Columns Added:")
        print("   • Product.capital_ratio - For profit calculations")
        print("   • Customer.total_credit - Total credit given")
        print("   • Customer.total_paid - Total payments received")
        print("   • Transaction.cashier_id - Links transactions to cashiers")
        print("   • Cashier.is_active - Active/inactive status")
        print("   • CreditTransaction.transaction_type - 'credit' or 'payment'")
        print("   • CreditTransaction.is_paid - Payment status tracking")

        print("\n🌐 Access your POS system at: http://localhost:5000")
        print("=" * 60)

if __name__ == "__main__":
    init_sample_data()



